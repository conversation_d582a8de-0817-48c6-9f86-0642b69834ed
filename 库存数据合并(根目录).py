import pandas as pd
import logging
import os
import re
from datetime import datetime, timedelta
import glob

# 配置日志记录器
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AutoDataMerger:
    def __init__(self):
        self.inventory_data = None
        self.visitors_and_buyers_data = []
        self.personal_statistics = []
        
    def classify_files(self):
        """根据文件名模式自动分类Excel文件"""
        excel_files = glob.glob("*.xls") + glob.glob("*.xlsx")
        
        inventory_files = []
        visitor_buyer_files = []
        seven_day_files = []
        
        # 生意参谋文件的日期模式
        business_pattern = r'【生意参谋平台】商品_全部_(\d{4}-\d{2}-\d{2})_(\d{4}-\d{2}-\d{2})'
        
        for file in excel_files:
            if file.startswith('库存信息'):
                inventory_files.append(file)
                logging.info(f"识别库存文件: {file}")
            elif '【生意参谋平台】' in file:
                match = re.search(business_pattern, file)
                if match:
                    start_date = match.group(1)
                    end_date = match.group(2)
                    
                    if start_date == end_date:
                        visitor_buyer_files.append(file)
                        logging.info(f"识别访客买家数文件: {file} (单日: {start_date})")
                    else:
                        seven_day_files.append(file)
                        logging.info(f"识别七天统计文件: {file} (范围: {start_date} 到 {end_date})")
                else:
                    logging.warning(f"无法解析生意参谋文件日期: {file}")
        
        return inventory_files, visitor_buyer_files, seven_day_files
    
    def extract_date_from_visitor_file(self, filename):
        """从访客买家数文件名中提取日期"""
        pattern = r'【生意参谋平台】商品_全部_(\d{4}-\d{2}-\d{2})_\d{4}-\d{2}-\d{2}'
        match = re.search(pattern, filename)
        if match:
            return match.group(1)
        return None
    
    def generate_export_filename(self, visitor_buyer_files):
        """生成导出文件名（日期+1天）"""
        if not visitor_buyer_files:
            # 如果没有访客买家数文件，使用当前日期
            tomorrow = datetime.now() + timedelta(days=1)
            return f"{tomorrow.strftime('%m-%d')}.xlsx"

        # 从第一个访客买家数文件中提取日期
        date_str = self.extract_date_from_visitor_file(visitor_buyer_files[0])
        if date_str:
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                next_day = date_obj + timedelta(days=1)
                return f"{next_day.strftime('%m-%d')}.xlsx"
            except ValueError:
                logging.error(f"日期解析失败: {date_str}")

        # 备用方案：使用当前日期
        tomorrow = datetime.now() + timedelta(days=1)
        return f"{tomorrow.strftime('%m-%d')}.xlsx"
    
    def preprocess_data(self, df, file_path):
        """预处理生意参谋数据（去除GUI依赖的版本）"""
        logging.info(f"处理文件: {file_path}")
        
        # 打印表格的前10行,以便了解其结构
        logging.info("表格前10行的内容:")
        for i in range(min(10, len(df))):
            logging.info(f"第{i+1}行: {df.iloc[i].tolist()}")
        
        # 读取第4行作为表头
        new_header = df.iloc[3]
        logging.info(f"第4行内容(将作为表头): {new_header.tolist()}")
        
        # 删除前4行(包括表头行),数据从第5行开始
        df = df.iloc[4:].reset_index(drop=True)
        logging.info("删除表格的1-4行的内容(包括表头行)")
        
        # 设置新表头
        df.columns = new_header
        logging.info(f"设置新表头后的列名: {df.columns.tolist()}")
        
        # 将文本类型的数值列转换为数值类型并去除千位分隔符
        for column in df.columns:
            if pd.api.types.is_string_dtype(df[column]) and df[column].str.contains(',', na=False).any():
                df.loc[:, column] = pd.to_numeric(df[column].str.replace(',', ''), errors='coerce')
        logging.info("遍历所有列,将文本类型的数值列转换为数值类型并去除千位分隔符")
        
        return df
    
    def process_inventory_files(self, inventory_files):
        """处理库存文件"""
        if not inventory_files:
            logging.warning("未找到库存文件")
            return
        
        # 使用第一个库存文件
        file_path = inventory_files[0]
        logging.info(f"处理库存文件: {file_path}")
        
        try:
            self.inventory_data = pd.read_excel(file_path)
            self.inventory_data['总库存'] = self.inventory_data.groupby('宝贝名称')['规格库存'].transform('sum')
            self.inventory_data = self.inventory_data.drop_duplicates(subset=['宝贝名称', '宝贝商家编码']).reset_index(drop=True)
            logging.info("库存文件处理完成")
        except Exception as e:
            logging.error(f"处理库存文件时出错: {str(e)}")
            raise
    
    def process_visitor_buyer_files(self, visitor_buyer_files):
        """处理访客买家数文件"""
        if not visitor_buyer_files:
            logging.warning("未找到访客买家数文件")
            return
        
        for index, file_path in enumerate(visitor_buyer_files, start=1):
            try:
                df = pd.read_excel(file_path)
                df = self.preprocess_data(df, file_path)
                
                # 检查是否存在我们需要的列
                required_columns = ['商品名称', '商品访客数', '支付买家数']
                if all(col in df.columns for col in required_columns):
                    # 保留商品ID列,用于后续可能的商家编码替换
                    if '商品ID' in df.columns:
                        df = df[['商品名称', '商品ID', '商品访客数', '支付买家数']]
                    else:
                        df = df[required_columns]
                    
                    df.rename(columns={'商品名称': '宝贝名称'}, inplace=True)
                    self.visitors_and_buyers_data.append(df)
                    logging.info(f"成功处理第{index}个访客买家数文件")
                else:
                    missing_cols = [col for col in required_columns if col not in df.columns]
                    logging.error(f"文件 {file_path} 缺少必要的列: {missing_cols}")
                    logging.error(f"当前列名: {df.columns.tolist()}")
                    raise ValueError(f"文件 {os.path.basename(file_path)} 缺少必要的列: {missing_cols}")
            except Exception as e:
                logging.error(f"处理文件 {file_path} 时出错: {str(e)}")
                raise
        
        logging.info(f"已处理 {len(self.visitors_and_buyers_data)} 个访客买家数文件")
    
    def process_seven_day_files(self, seven_day_files):
        """处理七天统计文件"""
        if not seven_day_files:
            logging.warning("未找到七天统计文件")
            return
        
        for index, file_path in enumerate(seven_day_files, start=1):
            try:
                df = pd.read_excel(file_path)
                df = self.preprocess_data(df, file_path)
                
                # 检查列名是否存在
                required_columns = ['商品名称', '商品访客数', '支付买家数']
                if all(col in df.columns for col in required_columns):
                    # 保留商品ID列,用于后续可能的商家编码替换
                    if '商品ID' in df.columns:
                        df = df[['商品名称', '商品ID', '商品访客数', '支付买家数']]
                    else:
                        df = df[required_columns]
                        
                    df.rename(columns={
                        '商品名称': '宝贝名称',
                        '商品访客数': '最近7天商品访客数',
                        '支付买家数': '最近7天支付买家数'
                    }, inplace=True)
                    self.personal_statistics.append(df)
                    logging.info(f"成功处理第{index}个七天统计文件")
                else:
                    missing_cols = [col for col in required_columns if col not in df.columns]
                    logging.error(f"文件 {file_path} 缺少必要的列: {missing_cols}")
                    logging.error(f"当前列名: {df.columns.tolist()}")
                    raise ValueError(f"文件 {os.path.basename(file_path)} 缺少必要的列: {missing_cols}")
            except Exception as e:
                logging.error(f"处理文件 {file_path} 时出错: {str(e)}")
                raise
        
        logging.info(f"已处理 {len(self.personal_statistics)} 个七天统计文件")

    def merge_and_export(self, export_filename):
        """合并所有数据并导出"""
        if not self.visitors_and_buyers_data:
            raise ValueError("请先导入访客买家数文件")

        # 合并访客和买家数据
        visitors_and_buyers_merged = pd.concat(self.visitors_and_buyers_data, ignore_index=True)

        if self.personal_statistics:
            # 合并七天统计数据
            personal_statistics_merged = pd.concat(self.personal_statistics, ignore_index=True)

            # 将访客买家数据与七天统计数据合并
            if '商品ID' in visitors_and_buyers_merged.columns and '商品ID' in personal_statistics_merged.columns:
                # 如果两个表都包含商品ID,使用宝贝名称和商品ID组合作为合并键
                merged_data = pd.merge(visitors_and_buyers_merged, personal_statistics_merged,
                                       on=['宝贝名称', '商品ID'], how='outer')
            else:
                # 否则仅使用宝贝名称作为合并键
                merged_data = pd.merge(visitors_and_buyers_merged, personal_statistics_merged,
                                       on='宝贝名称', how='outer')
        else:
            merged_data = visitors_and_buyers_merged

        # 如果存在商品ID列,将其重命名为临时名称,避免与库存表中的宝贝商家编码混淆
        if '商品ID' in merged_data.columns:
            merged_data.rename(columns={'商品ID': '_tmp_商品ID'}, inplace=True)

        if self.inventory_data is not None:
            # 将库存数据与之前合并后的数据再次合并
            if '_tmp_商品ID' in merged_data.columns:
                # 使用宝贝名称和商品ID组合作为合并键
                logging.info("使用宝贝名称和商品ID/商家编码组合进行合并")
                # 创建临时列用于合并
                merged_data['_merge_key'] = merged_data['宝贝名称'] + '_' + merged_data['_tmp_商品ID'].astype(str)
                self.inventory_data['_merge_key'] = self.inventory_data['宝贝名称'] + '_' + self.inventory_data['宝贝商家编码'].astype(str)

                # 使用_merge_key进行合并
                merged_result = pd.merge(merged_data, self.inventory_data[['_merge_key', '宝贝名称', '宝贝商家编码', '总库存']],
                                        on='_merge_key', how='outer', suffixes=('', '_inv'))

                # 处理合并后的数据
                mask = merged_result['宝贝名称_inv'].notna()
                if '宝贝名称_inv' in merged_result.columns:
                    merged_result.loc[mask, '宝贝名称'] = merged_result.loc[mask, '宝贝名称_inv']
                    merged_result.drop('宝贝名称_inv', axis=1, inplace=True)

                # 处理宝贝商家编码
                merged_result['宝贝商家编码'] = merged_result['宝贝商家编码'].fillna(merged_result['_tmp_商品ID'])

                # 删除临时列
                merged_result.drop(['_merge_key', '_tmp_商品ID'], axis=1, errors='ignore', inplace=True)
                merged_data = merged_result
            else:
                # 如果没有商品ID,则使用宝贝名称进行合并
                merged_data = pd.merge(merged_data, self.inventory_data[['宝贝名称', '宝贝商家编码', '总库存']],
                                      on=['宝贝名称'], how='outer')

        # 整理最终的数据表格
        columns_to_keep = ['宝贝名称', '宝贝商家编码', '总库存', '商品访客数', '支付买家数',
                           '最近7天商品访客数', '最近7天支付买家数']
        merged_data = merged_data[[col for col in columns_to_keep if col in merged_data.columns]]

        # 确保数值列都是数值类型
        numeric_columns = ['总库存', '商品访客数', '支付买家数', '最近7天商品访客数', '最近7天支付买家数']
        for col in numeric_columns:
            if col in merged_data.columns:
                merged_data[col] = pd.to_numeric(merged_data[col], errors='coerce')

        # 只对数值列填充0,非数值列(如宝贝商家编码)保持原样
        for col in numeric_columns:
            if col in merged_data.columns:
                merged_data[col] = merged_data[col].fillna(0)

        # 按商品访客数排序
        try:
            merged_data.sort_values(by='商品访客数', ascending=False, inplace=True)
        except TypeError as e:
            logging.error(f"排序时出错: {str(e)}")
            logging.error(f"商品访客数列的数据类型: {merged_data['商品访客数'].dtype}")
            logging.error(f"商品访客数列的前几个值: {merged_data['商品访客数'].head().tolist()}")
            raise

        # 去除完全重复的行
        before_dedup = len(merged_data)
        merged_data = merged_data.drop_duplicates()
        after_dedup = len(merged_data)
        if before_dedup > after_dedup:
            logging.info(f"已去除 {before_dedup - after_dedup} 行完全重复的数据")

        # 导出最终的表格文件
        try:
            merged_data.to_excel(export_filename, index=False)
            logging.info(f"已导出合并后的表格: {export_filename}")
            return True
        except Exception as e:
            logging.error(f"导出文件时出错: {str(e)}")
            raise

    def run(self):
        """运行完整的数据合并流程"""
        try:
            logging.info("开始自动库存数据合并流程")

            # 1. 分类文件
            inventory_files, visitor_buyer_files, seven_day_files = self.classify_files()

            if not inventory_files and not visitor_buyer_files and not seven_day_files:
                logging.error("未找到任何可处理的Excel文件")
                return False

            # 2. 生成导出文件名
            export_filename = self.generate_export_filename(visitor_buyer_files)
            logging.info(f"导出文件名: {export_filename}")

            # 3. 处理各类文件
            if inventory_files:
                self.process_inventory_files(inventory_files)

            if visitor_buyer_files:
                self.process_visitor_buyer_files(visitor_buyer_files)

            if seven_day_files:
                self.process_seven_day_files(seven_day_files)

            # 4. 合并并导出
            if self.visitors_and_buyers_data or self.personal_statistics:
                self.merge_and_export(export_filename)
                logging.info("数据合并流程完成")
                return True
            else:
                logging.error("没有可合并的数据")
                return False

        except Exception as e:
            logging.error(f"数据合并流程出错: {str(e)}")
            return False

def main():
    """主函数"""
    merger = AutoDataMerger()
    success = merger.run()

    if success:
        print("✅ 数据合并完成！")
    else:
        print("❌ 数据合并失败，请检查日志信息")

if __name__ == "__main__":
    main()
